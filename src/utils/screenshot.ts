import ScreenShot from 'js-web-screen-shot'
import { ScreenshotService } from '~/electron/render'

// Electron 桌面捕获约束类型
interface ElectronMediaConstraints {
  audio?: boolean | {
    mandatory: {
      chromeMediaSource: 'desktop'
    }
  }
  video: {
    mandatory: {
      chromeMediaSource: 'desktop'
      chromeMediaSourceId: string
    }
  }
}

// 全局变量用于跟踪当前的媒体流，确保资源正确释放
let currentStream: MediaStream | null = null
let lastScreenshotTime = 0
const SCREENSHOT_DEBOUNCE_TIME = 1000 // 1秒防抖

/**
 * 释放媒体流资源
 * @param stream 要释放的媒体流
 */
function releaseStream(stream: MediaStream | null): void {
  if (stream) {
    stream.getTracks().forEach((track) => {
      track.stop()
    })
  }
}

/**
 * 获取指定id设备的视频流
 * @param source 视频源对象
 * @param source.id 视频源ID
 * @param audio 是否包含音频，默认为false
 * @returns Promise<MediaStream | null> 返回媒体流或null
 */
function getInitStream(
  source: { id: string },
  audio = false,
): Promise<MediaStream | null> {
  return new Promise((resolve, reject) => {
    // 释放之前的流
    if (currentStream) {
      releaseStream(currentStream)
      currentStream = null
    }

    // 获取指定窗口的媒体流
    // 使用类型断言来处理 Electron 特有的 getUserMedia 参数
    const constraints: ElectronMediaConstraints = {
      audio: audio
        ? {
            mandatory: {
              chromeMediaSource: 'desktop',
            },
          }
        : false,
      video: {
        mandatory: {
          chromeMediaSource: 'desktop',
          chromeMediaSourceId: source.id,
        },
      },
    }

    ;(navigator.mediaDevices.getUserMedia as any)(constraints)
      .then((stream: MediaStream) => {
        currentStream = stream
        resolve(stream)
      })
      .catch((error: any) => {
        console.error('获取媒体流失败:', error)
        // 对于权限相关的错误，应该抛出而不是返回null
        if (error.name === 'NotAllowedError' || error.name === 'PermissionDeniedError') {
          reject(new Error('没有屏幕录制权限，请在系统设置中授权'))
        }
        else {
          resolve(null)
        }
      })
  })
}
/**
 * 清理截图资源
 */
function cleanupScreenshotResources(): void {
  if (currentStream) {
    releaseStream(currentStream)
    currentStream = null
  }
}

/**
 * 获取截图实例
 * @returns Promise<ScreenShot | null> 返回截图实例或null
 */
export async function getScreenshot(): Promise<ScreenShot | null> {
  // 防抖检查：避免频繁调用
  const now = Date.now()
  if (now - lastScreenshotTime < SCREENSHOT_DEBOUNCE_TIME) {
    console.warn('截图调用过于频繁，请稍后再试')
    return null
  }
  lastScreenshotTime = now

  try {
    const res = await ScreenshotService.getSources(1)
    if (!res.success) {
      throw new Error(res.error || '获取截图源失败')
    }

    if (!res.data?.sources || res.data.sources.length === 0) {
      throw new Error('没有可用的截图源')
    }

    // 优先选择屏幕源，如果没有则选择首页窗口
    const targetSource = res.data.sources.filter(e => e.type === 'screen')[0] || res.data.sources[0]
    if (!targetSource) {
      throw new Error('没有找到合适的截图源')
    }

    const stream = await getInitStream(targetSource)
    if (!stream) {
      throw new Error('获取视频流失败')
    }

    const screenshot = new ScreenShot({
      enableWebRtc: true, // 启用webrtc
      screenFlow: stream, // 传入屏幕流数据
      level: 9999, // 截图容器层级
      noScroll: false, // 是否禁止滚动
      completeCallback: ({ base64, cutInfo }) => {
        // 截图完成后清理资源
        cleanupScreenshotResources()
        // 这里可以添加自定义的完成回调逻辑
        if (process.env.NODE_ENV === 'development') {
          console.log('截图完成:', { base64: `${base64.substring(0, 50)}...`, cutInfo })
        }
      },
      closeCallback: () => {
        // 截图窗口关闭后清理资源
        cleanupScreenshotResources()
        if (process.env.NODE_ENV === 'development') {
          console.log('截图窗口关闭')
        }
      },
      saveCallback: (code, _msg) => {
        // code为0时代表保存成功
        if (process.env.NODE_ENV === 'development') {
          console.log('截图保存结果:', code === 0 ? '成功' : '失败')
        }
      },
    })

    return screenshot
  }
  catch (error) {
    // 发生错误时清理资源
    cleanupScreenshotResources()
    console.error('截图过程中发生错误:', error)
    throw error // 重新抛出错误，让调用者处理
  }
}
