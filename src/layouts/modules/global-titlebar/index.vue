<script setup lang="ts">
import { useFullscreen } from '@vueuse/core'
import GlobalSearch from '../global-search/index.vue'
import ThemeButton from '../global-header/components/theme-button.vue'
import UserAvatar from '../global-header/components/user-avatar.vue'
import GlobalBreadcrumb from '../global-breadcrumb/index.vue'
import GlobalBrowserNavigation from '../globar-browser-navigation/index.vue'
import UpdateButton from '@/components/common/update-button.vue'
import { useThemeStore } from '@/store/modules/theme'
import { useAppStore } from '@/store/modules/app'

defineOptions({
  name: 'GlobalTitlebar',
})
const appStore = useAppStore()
const themeStore = useThemeStore()
const { isFullscreen, toggle } = useFullscreen()

// 计算是否是垂直混合模式
const isVerticalMix = computed(() => themeStore.layout.mode === 'vertical-mix')

// 计算是否是水平混合模式
const isHorizontalMix = computed(() => themeStore.layout.mode === 'horizontal-mix')
const darkMenu = computed(() => !themeStore.darkMode && !isHorizontalMix.value && themeStore.sider.inverted)

// 主题相关的样式类
const themeClasses = computed(() => ({
  'bg-white': themeStore.themeScheme === 'light',
}))

// 获取侧边栏宽度（根据折叠状态动态计算）
function getSiderWidth() {
  const { reverseHorizontalMix } = themeStore.layout
  const { width, mixWidth, collapsedWidth, mixCollapsedWidth } = themeStore.sider

  // 如果是水平混合模式且反向布局
  if (isHorizontalMix.value && reverseHorizontalMix) {
    // 这里需要根据实际的菜单状态来判断，暂时返回基础宽度
    return appStore.siderCollapse ? collapsedWidth : width
  }

  // 根据布局模式选择基础宽度
  const baseWidth = isVerticalMix.value || isHorizontalMix.value ? mixWidth : width
  const baseCollapsedWidth = isVerticalMix.value || isHorizontalMix.value ? mixCollapsedWidth : collapsedWidth

  return appStore.siderCollapse ? baseCollapsedWidth : baseWidth
}

const siderStyle = computed(() => {
  const width = getSiderWidth()
  const isFullContentMode = appStore.fullContent

  // 基础样式
  const style = {
    width: `${width}px`,
    backgroundColor: 'transparent',
    borderBottom: 'none',
  }

  if (isFullContentMode) {
    // 在fullContent模式下
    style.backgroundColor = themeStore.themeScheme === 'light' ? '#ffffff' : ''
    style.borderBottom = '1px solid #eee'

    // 当菜单没有折叠时，需要特殊处理宽度
    if (!appStore.siderCollapse) {
      const { reverseHorizontalMix } = themeStore.layout
      const { collapsedWidth, mixCollapsedWidth } = themeStore.sider

      style.width = isHorizontalMix.value && reverseHorizontalMix
        ? `${collapsedWidth}px`
        : `${isVerticalMix.value || isHorizontalMix.value ? mixCollapsedWidth : collapsedWidth}px`
    }
  }

  return style
})
</script>

<template>
  <DarkModeContainer class="shadow-sider" :inverted="darkMenu">
    <div
      class="draggable h-[35px] flex items-center"
    >
      <!-- 侧边栏占位区域，添加宽度过渡动画 -->
      <div
        class="h-[35px] flex-shrink-0 border-b transition-all-300"
        :style="siderStyle"
      />
      <!-- 标题栏主体区域 -->
      <div class="h-full flex-y-center flex-1 flex-shrink-0 justify-between border-b" :class="themeClasses">
        <div class="h-full flex-y-center">
          <MenuToggler
            :collapsed="appStore.siderCollapse"
            @click="appStore.toggleSiderCollapse"
          />
          <GlobalBrowserNavigation />
          <GlobalBreadcrumb
            v-if="!appStore.isMobile"
          />
        </div>
        <div class="h-full flex-y-center">
          <GlobalSearch />
          <FullScreen
            v-if="!appStore.isMobile"
            :full="isFullscreen"
            @click="toggle"
          />
          <ThemeSchemaSwitch
            :theme-schema="themeStore.themeScheme"
            :is-dark="themeStore.darkMode"
            @switch="themeStore.toggleThemeScheme"
          />
          <UpdateButton v-if="!appStore.isMobile" />
          <ThemeButton />
          <UserAvatar />
        </div>
      </div>
    </div>
  </DarkModeContainer>
</template>

<style lang="scss" scoped>
.draggable{
  -webkit-app-region: drag;
}
</style>
