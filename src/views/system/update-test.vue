<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue'
import type { UpdateInfo } from '../../../electron/types/update-service'
import { UpdateStatus } from '../../../electron/types/update-service'
import {
  checkForUpdates,
  downloadUpdate,
  formatDownloadSpeed,
  formatFileSize,
  getCurrentVersion,
  getUpdateInfo,
  initializeUpdateService,
  installUpdate,
  isElectronEnvironment,
  onUpdateStatus,
} from '@/utils/update'

defineOptions({
  name: 'UpdateTest',
})

const updateInfo = ref<UpdateInfo>({ status: UpdateStatus.NOT_AVAILABLE })
const currentVersion = ref('未知版本')
const isLoading = ref(false)
let unsubscribeUpdateStatus: (() => void) | null = null

// 更新状态监听器
function handleUpdateStatus(info: UpdateInfo) {
  updateInfo.value = info
  console.log('更新状态变化:', info)
}

// 检查更新
async function handleCheckUpdate() {
  if (!isElectronEnvironment()) {
    window.$message?.error('当前不在Electron环境中')
    return
  }

  isLoading.value = true
  try {
    const result = await checkForUpdates()
    if (result.success) {
      window.$message?.success('检查更新请求已发送')
    }
    else {
      window.$message?.error(`检查更新失败: ${result.error}`)
    }
  }
  catch (error) {
    console.error('检查更新失败:', error)
    window.$message?.error('检查更新失败')
  }
  finally {
    isLoading.value = false
  }
}

// 下载更新
async function handleDownloadUpdate() {
  if (!isElectronEnvironment()) {
    window.$message?.error('当前不在Electron环境中')
    return
  }

  isLoading.value = true
  try {
    const result = await downloadUpdate()
    if (result.success) {
      window.$message?.success('下载更新请求已发送')
    }
    else {
      window.$message?.error(`下载更新失败: ${result.error}`)
    }
  }
  catch (error) {
    console.error('下载更新失败:', error)
    window.$message?.error('下载更新失败')
  }
  finally {
    isLoading.value = false
  }
}

// 安装更新
async function handleInstallUpdate() {
  if (!isElectronEnvironment()) {
    window.$message?.error('当前不在Electron环境中')
    return
  }

  try {
    const result = await installUpdate()
    if (result.success) {
      window.$message?.success('安装更新请求已发送，应用即将重启')
    }
    else {
      window.$message?.error(`安装更新失败: ${result.error}`)
    }
  }
  catch (error) {
    console.error('安装更新失败:', error)
    window.$message?.error('安装更新失败')
  }
}

// 获取更新信息
async function handleGetUpdateInfo() {
  if (!isElectronEnvironment()) {
    window.$message?.error('当前不在Electron环境中')
    return
  }

  try {
    const info = await getUpdateInfo()
    updateInfo.value = info
    window.$message?.success('更新信息已刷新')
  }
  catch (error) {
    console.error('获取更新信息失败:', error)
    window.$message?.error('获取更新信息失败')
  }
}

// 生命周期
onMounted(async () => {
  if (!isElectronEnvironment()) {
    window.$message?.warning('当前不在Electron环境中，更新功能不可用')
    return
  }

  // 初始化更新服务
  initializeUpdateService()

  // 添加更新状态监听器
  unsubscribeUpdateStatus = onUpdateStatus(handleUpdateStatus)

  // 获取当前版本和更新信息
  try {
    const [version, info] = await Promise.all([
      getCurrentVersion(),
      getUpdateInfo(),
    ])
    currentVersion.value = version
    updateInfo.value = info
  }
  catch (error) {
    console.error('初始化失败:', error)
  }
})

onUnmounted(() => {
  if (unsubscribeUpdateStatus) {
    unsubscribeUpdateStatus()
    unsubscribeUpdateStatus = null
  }
})
</script>

<template>
  <div class="p-6 space-y-6">
    <NCard title="Electron 更新测试">
      <div class="space-y-4">
        <!-- 版本信息 -->
        <div class="grid grid-cols-2 gap-4">
          <NCard size="small" title="当前版本">
            <div class="text-lg font-mono">
              {{ currentVersion }}
            </div>
          </NCard>

          <NCard size="small" title="更新状态">
            <div class="flex items-center gap-2">
              <NTag
                :type="updateInfo.status === UpdateStatus.ERROR ? 'error'
                  : updateInfo.status === UpdateStatus.AVAILABLE || updateInfo.status === UpdateStatus.DOWNLOADED ? 'success'
                    : 'default'"
              >
                {{ updateInfo.status }}
              </NTag>
              <span v-if="updateInfo.version" class="text-sm text-gray-500">
                v{{ updateInfo.version }}
              </span>
            </div>
          </NCard>
        </div>

        <!-- 下载进度 -->
        <div v-if="updateInfo.status === UpdateStatus.DOWNLOADING && updateInfo.progress">
          <NCard size="small" title="下载进度">
            <div class="space-y-2">
              <NProgress
                type="line"
                :percentage="updateInfo.progress.percent"
                :show-indicator="true"
                processing
              />
              <div class="flex justify-between text-sm text-gray-500">
                <span>{{ updateInfo.progress.percent.toFixed(1) }}%</span>
                <span>{{ formatDownloadSpeed(updateInfo.progress.bytesPerSecond) }}</span>
              </div>
              <div class="text-xs text-gray-400">
                {{ formatFileSize(updateInfo.progress.transferred) }} /
                {{ formatFileSize(updateInfo.progress.total) }}
              </div>
            </div>
          </NCard>
        </div>

        <!-- 发布说明 -->
        <div v-if="updateInfo.releaseNotes">
          <NCard size="small" title="发布说明">
            <div class="max-h-32 overflow-y-auto whitespace-pre-wrap text-sm">
              {{ updateInfo.releaseNotes }}
            </div>
          </NCard>
        </div>

        <!-- 错误信息 -->
        <div v-if="updateInfo.error">
          <NCard size="small" title="错误信息">
            <div class="text-sm text-red-500">
              {{ updateInfo.error }}
            </div>
          </NCard>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-wrap gap-3">
          <NButton
            type="primary"
            :loading="isLoading"
            @click="handleCheckUpdate"
          >
            检查更新
          </NButton>

          <NButton
            :disabled="updateInfo.status !== UpdateStatus.AVAILABLE"
            :loading="isLoading"
            @click="handleDownloadUpdate"
          >
            下载更新
          </NButton>

          <NButton
            :disabled="updateInfo.status !== UpdateStatus.DOWNLOADED"
            type="warning"
            @click="handleInstallUpdate"
          >
            安装更新
          </NButton>

          <NButton
            secondary
            @click="handleGetUpdateInfo"
          >
            刷新状态
          </NButton>
        </div>

        <!-- 调试信息 -->
        <NCard size="small" title="调试信息">
          <pre class="max-h-40 overflow-auto rounded bg-gray-50 p-2 text-xs">{{ JSON.stringify(updateInfo, null, 2) }}</pre>
        </NCard>
      </div>
    </NCard>
  </div>
</template>

<style scoped></style>
