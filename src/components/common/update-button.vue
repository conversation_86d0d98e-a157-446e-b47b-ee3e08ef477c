<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import type { UpdateInfo } from '../../../electron/types/update-service'
import { UpdateStatus } from '../../../electron/types/update-service'
import {
  checkForUpdates,
  downloadUpdate,
  getCurrentVersion,
  getUpdateInfo,
  getUpdateStatusText,
  initializeUpdateService,
  installUpdate,
  isElectronEnvironment,
  onUpdateStatus,
} from '@/utils/update'

defineOptions({
  name: 'UpdateButton',
})

// 更新信息状态
const updateInfo = ref<UpdateInfo>({ status: UpdateStatus.NOT_AVAILABLE })
const currentVersion = ref('未知版本')
const showDropdown = ref(false)
let unsubscribeUpdateStatus: (() => void) | null = null

// 计算属性
const hasUpdate = computed(() => {
  return [UpdateStatus.AVAILABLE, UpdateStatus.DOWNLOADED].includes(updateInfo.value.status)
})

const isChecking = computed(() => {
  return updateInfo.value.status === UpdateStatus.CHECKING
})

const isDownloading = computed(() => {
  return updateInfo.value.status === UpdateStatus.DOWNLOADING
})

const statusText = computed(() => {
  const baseText = getUpdateStatusText(updateInfo.value.status)
  if (updateInfo.value.status === UpdateStatus.AVAILABLE && updateInfo.value.version) {
    return `有新版本 ${updateInfo.value.version}`
  }
  if (updateInfo.value.status === UpdateStatus.DOWNLOADING && updateInfo.value.progress) {
    return `下载中 ${updateInfo.value.progress.percent.toFixed(0)}%`
  }
  return baseText
})

// 更新状态监听器
function handleUpdateStatus(info: UpdateInfo) {
  updateInfo.value = info
}

// 操作方法
async function handleCheckUpdate() {
  if (!isElectronEnvironment())
    return

  try {
    const result = await checkForUpdates()
    if (result.success) {
      window.$message?.info('正在检查更新...')
    }
    else {
      window.$message?.error(`检查更新失败: ${result.error}`)
    }
  }
  catch (error) {
    console.error('检查更新失败:', error)
    window.$message?.error('检查更新失败')
  }
}

async function handleDownload() {
  if (!isElectronEnvironment())
    return

  try {
    const result = await downloadUpdate()
    if (result.success) {
      window.$message?.info('开始下载更新...')
    }
    else {
      window.$message?.error(`下载更新失败: ${result.error}`)
    }
  }
  catch (error) {
    console.error('下载更新失败:', error)
    window.$message?.error('下载更新失败')
  }
}

async function handleInstall() {
  if (!isElectronEnvironment())
    return

  try {
    const result = await installUpdate()
    if (result.success) {
      window.$message?.info('开始安装更新，应用即将重启...')
    }
    else {
      window.$message?.error(`安装更新失败: ${result.error}`)
    }
  }
  catch (error) {
    console.error('安装更新失败:', error)
    window.$message?.error('安装更新失败')
  }
}

// 生命周期
onMounted(async () => {
  // 检查是否在Electron环境中
  if (!isElectronEnvironment()) {
    console.warn('当前不在Electron环境中，更新功能不可用')
    return
  }

  // 初始化更新服务
  initializeUpdateService()

  // 添加更新状态监听器
  unsubscribeUpdateStatus = onUpdateStatus(handleUpdateStatus)

  // 获取当前版本和更新状态
  try {
    const [version, info] = await Promise.all([
      getCurrentVersion(),
      getUpdateInfo(),
    ])
    currentVersion.value = version
    handleUpdateStatus(info)
  }
  catch (error) {
    console.error('获取版本信息失败:', error)
  }
})

onUnmounted(() => {
  // 移除监听器
  if (unsubscribeUpdateStatus) {
    unsubscribeUpdateStatus()
    unsubscribeUpdateStatus = null
  }
})
</script>

<template>
  <NDropdown
    v-model:show="showDropdown"
    trigger="click"
    placement="bottom-end"
  >
    <template #default>
      <div class="w-64 p-4 space-y-3">
        <!-- 版本信息 -->
        <div class="text-sm">
          <div class="mb-1 font-medium">
            当前版本
          </div>
          <div class="text-gray-600">
            {{ currentVersion }}
          </div>
        </div>

        <!-- 更新状态 -->
        <div class="text-sm">
          <div class="mb-1 font-medium">
            更新状态
          </div>
          <div class="text-gray-600">
            {{ statusText }}
          </div>
        </div>

        <!-- 下载进度 -->
        <div v-if="isDownloading && updateInfo.progress" class="text-sm">
          <div class="mb-2 font-medium">
            下载进度
          </div>
          <NProgress
            type="line"
            :percentage="updateInfo.progress.percent"
            :show-indicator="true"
            processing
          />
          <div class="mt-1 flex justify-between text-xs text-gray-500">
            <span>{{ updateInfo.progress.percent.toFixed(1) }}%</span>
            <span>
              {{ (updateInfo.progress.bytesPerSecond / 1024 / 1024).toFixed(1) }} MB/s
            </span>
          </div>
        </div>

        <!-- 新版本信息 -->
        <div v-if="updateInfo.version && hasUpdate" class="text-sm">
          <div class="mb-1 font-medium">
            新版本
          </div>
          <div class="text-gray-600">
            {{ updateInfo.version }}
          </div>
        </div>

        <!-- 发布说明 -->
        <div v-if="updateInfo.releaseNotes && hasUpdate" class="text-sm">
          <div class="mb-1 font-medium">
            更新内容
          </div>
          <div class="max-h-20 overflow-y-auto whitespace-pre-wrap text-xs text-gray-600">
            {{ updateInfo.releaseNotes }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex gap-2 border-t pt-2">
          <NButton
            v-if="updateInfo.status === UpdateStatus.NOT_AVAILABLE || updateInfo.status === UpdateStatus.ERROR"
            size="small"
            type="primary"
            :loading="isChecking"
            @click="handleCheckUpdate"
          >
            检查更新
          </NButton>

          <NButton
            v-if="updateInfo.status === UpdateStatus.AVAILABLE"
            size="small"
            type="primary"
            @click="handleDownload"
          >
            下载更新
          </NButton>

          <NButton
            v-if="updateInfo.status === UpdateStatus.DOWNLOADED"
            size="small"
            type="primary"
            @click="handleInstall"
          >
            立即安装
          </NButton>
        </div>
      </div>
    </template>
  </NDropdown>
</template>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
