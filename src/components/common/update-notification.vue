<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref } from 'vue'
import type { UpdateInfo } from '../../../electron/types/update-service'
import { UpdateStatus } from '../../../electron/types/update-service'
import {
  checkForUpdates,
  downloadUpdate,
  formatDownloadSpeed,
  getUpdateInfo,
  getUpdateStatusText,
  initializeUpdateService,
  installUpdate,
  isElectronEnvironment,
  onUpdateStatus,
} from '@/utils/update'

defineOptions({
  name: 'UpdateNotification',
})

// 更新信息状态
const updateInfo = ref<UpdateInfo>({ status: UpdateStatus.NOT_AVAILABLE })
const showNotification = ref(false)
const isDownloading = ref(false)
let unsubscribeUpdateStatus: (() => void) | null = null

// 计算属性
const statusText = computed(() => {
  const baseText = getUpdateStatusText(updateInfo.value.status)
  if (updateInfo.value.status === UpdateStatus.AVAILABLE && updateInfo.value.version) {
    return `${baseText} ${updateInfo.value.version}`
  }
  if (updateInfo.value.status === UpdateStatus.ERROR && updateInfo.value.error) {
    return `${baseText}: ${updateInfo.value.error}`
  }
  return baseText
})

const progressPercent = computed(() => {
  return updateInfo.value.progress?.percent || 0
})

const downloadSpeed = computed(() => {
  const speed = updateInfo.value.progress?.bytesPerSecond || 0
  return formatDownloadSpeed(speed)
})

const showProgress = computed(() => {
  return updateInfo.value.status === UpdateStatus.DOWNLOADING && updateInfo.value.progress
})

const showActions = computed(() => {
  return [UpdateStatus.AVAILABLE, UpdateStatus.DOWNLOADED, UpdateStatus.ERROR].includes(updateInfo.value.status)
})

// 更新状态监听器
function handleUpdateStatus(info: UpdateInfo) {
  updateInfo.value = info

  // 显示通知的条件
  if ([
    UpdateStatus.AVAILABLE,
    UpdateStatus.DOWNLOADED,
    UpdateStatus.ERROR,
  ].includes(info.status)) {
    showNotification.value = true
  }

  // 自动隐藏某些状态的通知
  if ([
    UpdateStatus.NOT_AVAILABLE,
    UpdateStatus.INSTALLING,
  ].includes(info.status)) {
    showNotification.value = false
  }
}

// 操作方法
async function handleDownload() {
  if (!isElectronEnvironment())
    return

  isDownloading.value = true
  try {
    const result = await downloadUpdate()
    if (result.success) {
      window.$message?.info('开始下载更新...')
    }
    else {
      window.$message?.error(`下载更新失败: ${result.error}`)
    }
  }
  catch (error) {
    console.error('下载更新失败:', error)
    window.$message?.error('下载更新失败')
  }
  finally {
    isDownloading.value = false
  }
}

async function handleInstall() {
  if (!isElectronEnvironment())
    return

  try {
    const result = await installUpdate()
    if (result.success) {
      window.$message?.info('开始安装更新，应用即将重启...')
    }
    else {
      window.$message?.error(`安装更新失败: ${result.error}`)
    }
  }
  catch (error) {
    console.error('安装更新失败:', error)
    window.$message?.error('安装更新失败')
  }
}

async function handleCheckUpdate() {
  if (!isElectronEnvironment())
    return

  try {
    const result = await checkForUpdates()
    if (result.success) {
      window.$message?.info('正在检查更新...')
    }
    else {
      window.$message?.error(`检查更新失败: ${result.error}`)
    }
  }
  catch (error) {
    console.error('检查更新失败:', error)
    window.$message?.error('检查更新失败')
  }
}

function handleClose() {
  showNotification.value = false
}

// 生命周期
onMounted(async () => {
  // 检查是否在Electron环境中
  if (!isElectronEnvironment()) {
    console.warn('当前不在Electron环境中，更新功能不可用')
    return
  }

  // 初始化更新服务
  initializeUpdateService()

  // 添加更新状态监听器
  unsubscribeUpdateStatus = onUpdateStatus(handleUpdateStatus)

  // 获取当前更新状态
  try {
    const info = await getUpdateInfo()
    handleUpdateStatus(info)
  }
  catch (error) {
    console.error('获取更新信息失败:', error)
  }
})

onUnmounted(() => {
  // 移除监听器
  if (unsubscribeUpdateStatus) {
    unsubscribeUpdateStatus()
    unsubscribeUpdateStatus = null
  }
})
</script>

<template>
  <div v-if="showNotification" class="fixed right-4 top-4 z-50">
    <NCard class="w-80 shadow-lg">
      <template #header>
        <div class="flex items-center justify-between">
          <span class="font-medium">应用更新</span>
          <NButton quaternary circle size="small" @click="handleClose">
            <template #icon>
              <SvgIcon icon="material-symbols:close" />
            </template>
          </NButton>
        </div>
      </template>

      <div class="space-y-3">
        <!-- 状态文本 -->
        <div class="text-sm text-gray-600">
          {{ statusText }}
        </div>

        <!-- 下载进度 -->
        <div v-if="showProgress" class="space-y-2">
          <NProgress
            type="line"
            :percentage="progressPercent"
            :show-indicator="true"
            processing
          />
          <div class="flex justify-between text-xs text-gray-500">
            <span>{{ progressPercent.toFixed(1) }}%</span>
            <span>{{ downloadSpeed }}</span>
          </div>
        </div>

        <!-- 发布说明 -->
        <div v-if="updateInfo.releaseNotes && updateInfo.status === UpdateStatus.AVAILABLE" class="max-h-20 overflow-y-auto text-xs text-gray-500">
          <div class="mb-1 font-medium">
            更新内容:
          </div>
          <div class="whitespace-pre-wrap">
            {{ updateInfo.releaseNotes }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div v-if="showActions" class="flex justify-end gap-2">
          <NButton
            v-if="updateInfo.status === UpdateStatus.AVAILABLE"
            size="small"
            type="primary"
            :loading="isDownloading"
            @click="handleDownload"
          >
            下载更新
          </NButton>

          <NButton
            v-if="updateInfo.status === UpdateStatus.DOWNLOADED"
            size="small"
            type="primary"
            @click="handleInstall"
          >
            立即安装
          </NButton>

          <NButton
            v-if="updateInfo.status === UpdateStatus.ERROR"
            size="small"
            type="primary"
            @click="handleCheckUpdate"
          >
            重新检查
          </NButton>

          <NButton size="small" @click="handleClose">
            稍后处理
          </NButton>
        </div>
      </div>
    </NCard>
  </div>
</template>

<style scoped>
.update-notification-enter-active,
.update-notification-leave-active {
  transition: all 0.3s ease;
}

.update-notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.update-notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}
</style>
